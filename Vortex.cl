// Vortex.cl - Volumetric Raymarching Shader
// Converted from GLSL to Houdini OpenCL @-Binding format
// Original: Complex volumetric raymarching with distortion effects
//
// This shader creates a swirling vortex effect using:
// 1. Volumetric raymarching through a distorted hollow sphere
// 2. Turbulence-based distortion using multiple octaves
// 3. Color accumulation with depth-based attenuation
// 4. Tanh tonemapping for HDR-like appearance
//
// Visual Effect: Creates a swirling, glowing vortex with organic turbulence

#include <matrix.h>

// Custom functions for OpenCL compatibility
float __attribute__((overloadable)) fract(float x) {
    return x - floor(x);
}

float2 __attribute__((overloadable)) fract(float2 x) {
    return x - floor(x);
}

float3 __attribute__((overloadable)) fract(float3 x) {
    return x - floor(x);
}

float4 __attribute__((overloadable)) fract(float4 x) {
    return x - floor(x);
}

// Tanh function for tonemapping
float __attribute__((overloadable)) tanh(float x) {
    float exp2x = exp(2.0f * x);
    return (exp2x - 1.0f) / (exp2x + 1.0f);
}

float4 __attribute__((overloadable)) tanh(float4 x) {
    return (float4)(tanh(x.x), tanh(x.y), tanh(x.z), tanh(x.w));
}

// Normalize function for float3
float3 __attribute__((overloadable)) normalize(float3 v) {
    float len = sqrt(v.x * v.x + v.y * v.y + v.z * v.z);
    return len > 0.0f ? v / len : (float3)(0.0f);
}

// Length function for float3
float __attribute__((overloadable)) length(float3 v) {
    return sqrt(v.x * v.x + v.y * v.y + v.z * v.z);
}

// Dot product for float2
float __attribute__((overloadable)) dot(float2 a, float2 b) {
    return a.x * b.x + a.y * b.y;
}

// Parameter Bindings
#bind layer &!vortex_output float4
#bind parm time float val=0.0                    // Animation time
#bind parm iterations int val=100                // Raymarching steps (quality vs performance)
#bind parm step_size float val=0.002             // Base step size for raymarching
#bind parm camera_distance float val=6.0         // Camera distance from origin
#bind parm distortion_strength float val=1.0     // Turbulence intensity
#bind parm glow_intensity float val=7000.0       // Tonemapping intensity
#bind parm sphere_radius float val=0.5           // Hollow sphere radius

@KERNEL
{
    // Get current pixel coordinates and resolution using built-in variables
    float2 fragCoord = @P;
    float2 iResolution = (float2)(@res);
    float iTime = @time;
    
    // Initialize output color
    float4 O = (float4)(0.0f);
    
    // Raymarch iterator
    float i = 0.0f;
    
    // Raymarch depth - initialize with noise based on pixel coordinates
    float z = fract(dot(fragCoord, sin(fragCoord)));
    
    // Raymarch step size
    float d;
    
    // Raymarch loop (configurable iterations)
    // Original loop: for(O *= i; i++<1e2; O+=(sin(z+vec4(6,2,4,0))+1.5)/d)
    for (int iter = 0; iter < @iterations; iter++) {
        // Original loop condition: i++<1e2
        i += 1.0f;
        if (i >= (float)@iterations) break;

        // Apply the O *= i operation from original loop header
        O *= i;

        // Raymarch sample position
        // Original: z * normalize(vec3(I+I,0) - iResolution.xyy)
        float3 ray_dir = (float3)(fragCoord.x + fragCoord.x - iResolution.x,
                                  fragCoord.y + fragCoord.y - iResolution.y,
                                  0.0f - iResolution.y);
        float3 p = z * normalize(ray_dir);

        // Shift camera back
        p.z += @camera_distance;

        // Distortion (turbulence) loop
        for (d = 1.0f; d < 9.0f; d /= 0.8f) {
            // Add distortion waves
            // Original: p += cos(p.yzx*d-iTime)/d;
            float3 wave_input = (float3)(p.y, p.z, p.x) * d - iTime;
            float3 wave = (float3)(cos(wave_input.x), cos(wave_input.y), cos(wave_input.z));
            p += @distortion_strength * wave / d;
        }

        // Compute distorted distance field of hollow sphere
        // Original: z += d = .002+abs(length(p)-.5)/4e1;
        float sphere_dist = fabs(length(p) - @sphere_radius);
        d = @step_size + sphere_dist / 40.0f;
        z += d;

        // Sample coloring and glow attenuation
        // Original loop increment: O+=(sin(z+vec4(6,2,4,0))+1.5)/d
        float4 color_phase = (float4)(z + 6.0f, z + 2.0f, z + 4.0f, z);
        float4 color_sin = (float4)(sin(color_phase.x), sin(color_phase.y), sin(color_phase.z), sin(color_phase.w));
        float4 color_contribution = (color_sin + 1.5f) / d;

        // Accumulate color
        O += color_contribution;
    }
    
    // Tanh tonemapping
    // O = tanh(O / 7e3);
    O = tanh(O / @glow_intensity);
    
    // Ensure alpha is 1.0
    O.w = 1.0f;
    
    // Output the final color
    @vortex_output.set(O);
}
