#bind layer &!chladni float

#bind layer !src?
#bind layer pos? float2

#bind layer a? float val=1
#bind layer b? float val=1
#bind layer m? float val=1
#bind layer n? float val=1

#bind parm a_parm float val=1
#bind parm b_parm float val=1
#bind parm m_parm float val=1
#bind parm n_parm float val=1

#bind parm tiledsize float2 val=2

#bind parm threshold float val=0.5
#bind parm width float val=0.1
#bind parm output int val=0

@KERNEL
{
    float amp = 0;
    
    float2 p;
    if (@pos.bound)
        p = @pos;
    else
        p = @P;
        
    p /= @tiledsize;
    
    // Find sum of amplitudes of both standing waves.
    amp = 0;
    amp += @a_parm * @a * sinpi(p.x * @n * @n_parm) * sinpi(p.y * @m * @m_parm);
    amp += @b_parm * @b * sinpi(p.x * @m * @m_parm) * sinpi(p.y * @n * @n_parm);
    
    // Threshold.
    if (@output == 0)
        amp = 1-smoothstep(@threshold-@width, @threshold+@width, fabs(amp));
    else if (@output == 1)
        amp = fabs(amp);
    else if (@output == 2)
        amp = amp;

    @chladni.set(amp);
}