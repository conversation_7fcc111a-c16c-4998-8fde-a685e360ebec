// Zippy fractal shader converted to <PERSON><PERSON><PERSON> 20.5 OpenCL @-Binding format
// Based on original GLSL shader

// Output layer - RGBA color output
#bind layer &!zippy_output float4

// Optional input layers
#bind layer !src?                    // Optional source image
#bind layer pos? float2              // Optional position override

// Animation and scale parameters
#bind parm time_scale float val=1.0
#bind parm position_scale float val=0.2
#bind parm iterations int val=19
#bind parm base_amplitude float val=0.5
#bind parm amplitude_increment float val=0.03

// Color and intensity parameters
#bind parm intensity_multiplier float val=25.6
#bind parm color_clamp float val=13.0
#bind parm color_divisor float val=164.0
#bind parm position_damping float val=250.0

// Resolution parameters
#bind parm resolution float2 val=1024

// Time parameter (can be animated)
#bind parm time float val=0.0

// Helper function to create 2x2 rotation matrix
float4 make_mat2(float angle) {
    float c = cos(angle);
    float s = sin(angle);
    return (float4)(c, -s, s, c);  // mat2 as float4: [m00, m01, m10, m11]
}

// Helper function to multiply 2x2 matrix with float2
float2 mat2_mul(float4 m, float2 v) {
    return (float2)(m.x * v.x + m.y * v.y, m.z * v.x + m.w * v.y);
}

@KERNEL
{
    // Get position - use pos layer if bound, otherwise use default @P
    float2 u;
    if (@pos.bound)
        u = @pos;
    else
        u = @P;

    // Get resolution
    float2 v = @resolution;

    // Normalize coordinates similar to GLSL fragCoord
    u = @position_scale * (u + u - v) / v.y;

    // Initialize output color and working variables
    float4 z = (float4)(1.0f, 2.0f, 3.0f, 0.0f);
    float4 o = z;

    // Main iteration loop
    float a = @base_amplitude;
    float t = @time * @time_scale;

    for (int i = 0; i < @iterations; i++) {
        float fi = (float)(i + 1);

        // Update time
        t += 1.0f;

        // Calculate complex transformation
        float dot_vv = dot(v, v);
        float dot_uu = dot(u, u);

        // Calculate sine term
        float2 u_yx = (float2)(u.y, u.x);
        float2 sine_arg = 1.5f * u / (0.5f - dot_uu) - 9.0f * u_yx + t;
        float2 sine_val = sin(sine_arg);

        // Calculate length term
        float len_term = length((1.0f + fi * dot_vv) * sine_val);

        // Add to output
        if (len_term > 1e-6f) {  // Avoid division by zero
            float4 cos_term = (1.0f + cos(z + t));
            o += cos_term / len_term;
        }

        // Update v
        float pow_term = pow(a += @amplitude_increment, fi);
        v = cos(t - 7.0f * u * pow_term) - 5.0f * u;

        // Create rotation matrix
        float4 rot_angles = (float4)(0.0f, 11.0f, 33.0f, 0.0f);
        float4 mat = make_mat2(fi + 0.02f * t - rot_angles.x);

        // Apply matrix transformation
        u = mat2_mul(mat, u);

        // Calculate complex update term
        float dot_uu_new = dot(u, u);
        float2 u_yx_new = (float2)(u.y, u.x);
        float cos_term_scalar = cos(100.0f * u_yx_new.x + t);

        // Update u with tanh and other terms
        u += tanh(40.0f * dot_uu_new * cos_term_scalar) / 200.0f
           + 0.2f * a * u
           + cos(4.0f / exp(dot(o, o) / 100.0f) + t) / 300.0f;
    }

    // Final color processing
    float4 min_o = min(o, @color_clamp);
    float4 final_color = @intensity_multiplier / (min_o + @color_divisor / o)
                        - dot(u, u) / @position_damping;

    // Ensure alpha is 1.0 for proper display
    final_color.w = 1.0f;

    // Output the result
    @zippy_output.set(final_color);
}
