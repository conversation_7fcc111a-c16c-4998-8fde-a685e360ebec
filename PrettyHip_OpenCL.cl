// PrettyHip shader converted to <PERSON><PERSON><PERSON> 20.5 OpenCL @-Binding format
// Based on original GLSL shader - creates animated square pattern with rotation

// Output layer - RGBA color output
#bind layer &!prettyhip_output float4

// Optional input layers
#bind layer !src?                    // Optional source image
#bind layer pos? float2              // Optional position override

// Animation and transformation parameters
#bind parm time_scale float val=1.0
#bind parm rotation_angle float val=45.0  // Rotation in degrees
#bind parm animate_rotation int val=0     // Toggle for animated rotation
#bind parm pattern_scale float val=10.0   // Scale of the pattern repetition

// Pattern parameters
#bind parm edge_speed float val=0.5       // Speed of edge animation
#bind parm edge_smoothness float val=0.05 // Smoothness of edges
#bind parm value_threshold float val=0.95 // Threshold for pattern visibility
#bind parm distance_influence float val=0.1  // How much distance affects brightness

// Color parameters
#bind parm base_color float3 val=1.0      // Base white color
#bind parm accent_color float3 val={0.5,0.75,1.0}  // Blue accent color
#bind parm alpha_multiplier float val=0.25 // Alpha channel multiplier

// Resolution parameters
#bind parm resolution float2 val=1024

// Time parameter (can be animated)
#bind parm time float val=0.0

// Custom functions for OpenCL compatibility
float __attribute__((overloadable)) fract(float x) {
    return x - floor(x);
}

float2 __attribute__((overloadable)) fract(float2 x) {
    return x - floor(x);
}

@KERNEL
{
    // Get position - use pos layer if bound, otherwise use default @P
    float2 fragCoord;
    if (@pos.bound)
        fragCoord = @pos;
    else
        fragCoord = @P;
    
    // Calculate aspect ratio
    float aspect = @resolution.y / @resolution.x;
    
    // Normalize coordinates
    float2 uv = fragCoord / @resolution.x;
    uv -= (float2)(0.5f, 0.5f * aspect);
    
    // Apply rotation
    float rot_degrees = @rotation_angle;
    if (@animate_rotation) {
        rot_degrees = @rotation_angle * sin(@time * @time_scale);
    }
    float rot = radians(rot_degrees);
    
    // Create rotation matrix using Houdini's mat2 type
    mat2 rotation_matrix = (mat2)(cos(rot), -sin(rot), sin(rot), cos(rot));
    
    // Apply rotation using Houdini's mat2vecmul function
    uv = mat2vecmul(rotation_matrix, uv);
    
    // Restore coordinates
    uv += (float2)(0.5f, 0.5f * aspect);
    uv.y += 0.5f * (1.0f - aspect);
    
    // Create pattern
    float2 pos = @pattern_scale * uv;
    float2 rep = fract(pos);
    
    // Calculate distance to edges
    float dist = 2.0f * min(min(rep.x, 1.0f - rep.x), min(rep.y, 1.0f - rep.y));
    
    // Calculate distance from center
    float squareDist = length((floor(pos) + (float2)(0.5f)) - (float2)(5.0f));
    
    // Calculate animated edge
    float edge = (@time * @time_scale - squareDist * @edge_speed) * 0.5f;
    edge = 2.0f * fract(edge * 0.5f);
    
    // Calculate pattern value
    float value = fract(dist * 2.0f);
    value = mix(value, 1.0f - value, step(1.0f, edge));
    
    // Apply edge effect
    edge = pow(fabs(1.0f - edge), 2.0f);
    value = smoothstep(edge - @edge_smoothness, edge, @value_threshold * value);
    
    // Add distance influence
    value += squareDist * @distance_influence;
    
    // Create final color
    float3 base_col = @base_color;
    float3 accent_col = @accent_color;
    float4 final_color = (float4)(mix(base_col, accent_col, value), 
                                  @alpha_multiplier * clamp(value, 0.0f, 1.0f));
    
    // Output the result
    @prettyhip_output.set(final_color);
}
